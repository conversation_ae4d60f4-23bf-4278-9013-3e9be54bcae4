"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useTopicEntryMutations } from "@/hooks/queries/useTopics";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import type {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import {
  Check,
  GripVertical,
  Lightbulb,
  MoreHorizontal,
  Trash2,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";
import { EditableCell } from "./EditableCell";

interface BusinessItemTableProps {
  itemDetails: BusinessItemDetail[];
  selectedBusinessItem: BusinessItem | null;
  onBackToItems: () => void;
  sectionId?: string;
  topicId?: string;
}

// Get status color helper - matching card styling
const getStatusColor = (status: string) => {
  switch (status) {
    case "idea":
      return "bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white border border-yellow-300 dark:border-yellow-700";
    case "action":
      return "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700";
    case "confirmed":
      return "bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 border border-green-700 dark:border-green-600";
    case "unproven":
      return "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600";
    default:
      return "bg-muted text-muted-foreground";
  }
};

// Helper function to determine status based on content
const getAutoStatus = (
  title: string,
  actions: string,
  result: string
): BusinessItemDetail["status"] => {
  const hasTitle = title.trim() !== "";
  const hasActions = actions.trim() !== "";
  const hasResult = result.trim() !== "";

  if (hasTitle && !hasActions && !hasResult) {
    return "idea";
  } else if (hasTitle && hasActions && !hasResult) {
    return "action";
  } else if (hasTitle && hasActions && hasResult) {
    return "confirmed"; // Default when all fields are filled
  }
  return "idea"; // Default fallback
};

// Helper function to check if status should be editable
const isStatusEditable = (
  title: string,
  actions: string,
  result: string
): boolean => {
  const hasTitle = title.trim() !== "";
  const hasActions = actions.trim() !== "";
  const hasResult = result.trim() !== "";

  // Only editable when all three fields are filled (can choose between confirmed/unproven)
  return hasTitle && hasActions && hasResult;
};

// StatusSelect component
function StatusSelect({
  value,
  onChange,
  placeholder,
  disabled = false,
  detail,
}: {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  detail: BusinessItemDetail;
}) {
  const editable = isStatusEditable(
    detail.title,
    detail.actions,
    detail.result
  );

  if (!editable) {
    // Show as non-editable badge with icon
    const autoStatus = getAutoStatus(
      detail.title,
      detail.actions,
      detail.result
    );

    const getStatusIcon = (status: string) => {
      switch (status) {
        case "idea":
          return <Lightbulb className="h-3 w-3" />;
        case "action":
          return <Zap className="h-3 w-3" />;
        case "confirmed":
          return <Check className="h-3 w-3" />;
        default:
          return null;
      }
    };

    return (
      <Badge
        className={`${getStatusColor(
          autoStatus
        )} capitalize text-xs px-1.5 py-0.5 h-6`}
      >
        {getStatusIcon(autoStatus)}
        {autoStatus}
      </Badge>
    );
  }

  return (
    <Select value={value} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger className="w-[120px] h-7 px-2 ">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="confirmed">
          <Badge
            className={`${getStatusColor(
              "confirmed"
            )} text-xs px-1.5 py-0.5 h-6`}
          >
            <Check className="h-3 w-3 mr-1" />
            Validated
          </Badge>
        </SelectItem>
        <SelectItem value="unproven">
          <Badge
            className={`${getStatusColor(
              "unproven"
            )} text-xs px-1.5 py-0.5 h-6`}
          >
            Invalidated
          </Badge>
        </SelectItem>
      </SelectContent>
    </Select>
  );
}

// DragHandle component
function DragHandle({ provided }: { provided: any }) {
  return (
    <div
      {...provided.dragHandleProps}
      className="cursor-grab py-3 px-3 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-muted/50"
      title="Drag to reorder"
    >
      <GripVertical className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
    </div>
  );
}

// BusinessItemRow component
function BusinessItemRow({
  detail,
  index,
  editingCell,
  setEditingCell,
  onSave,
  onStatusChange,
}: {
  detail: BusinessItemDetail;
  index: number;
  editingCell: { id: string; field: string } | null;
  setEditingCell: (cell: { id: string; field: string } | null) => void;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
  onStatusChange: (id: string, status: string) => void;
}) {
  return (
    <Draggable key={detail.id} draggableId={detail.id} index={index}>
      {(provided, snapshot) => (
        <TableRow
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`group hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 border-b border-gray-200 dark:border-gray-700 relative z-20 ${
            snapshot.isDragging
              ? "bg-gray-200 dark:bg-gray-700 shadow-lg scale-[1.02] rotate-1 z-30"
              : ""
          } ${
            index % 2 === 1 ? "bg-gray-50 dark:bg-gray-900" : "bg-background"
          }`}
        >
          <TableCell className="py-0 px-0 border-r border-gray-200 dark:border-gray-700">
            <DragHandle provided={provided} />
          </TableCell>
          <TableCell className="font-medium py-3 px-3 w-1/3 border-r border-gray-200 dark:border-gray-700">
            <EditableCell
              id={detail.id}
              field="title"
              value={detail.title}
              multiline={true}
              className="font-semibold"
              editingCell={editingCell}
              setEditingCell={setEditingCell}
              onSave={onSave}
            />
          </TableCell>
          <TableCell className="py-3 px-3 w-1/3 border-r border-gray-200 dark:border-gray-700">
            <EditableCell
              id={detail.id}
              field="actions"
              value={detail.actions}
              multiline={true}
              editingCell={editingCell}
              setEditingCell={setEditingCell}
              onSave={onSave}
            />
          </TableCell>
          <TableCell className="py-3 px-3 w-1/3 border-r border-gray-200 dark:border-gray-700">
            <EditableCell
              id={detail.id}
              field="result"
              value={detail.result}
              multiline={true}
              editingCell={editingCell}
              setEditingCell={setEditingCell}
              onSave={onSave}
            />
          </TableCell>
          <TableCell className="py-3 px-3 border-r border-gray-200 dark:border-gray-700">
            <StatusSelect
              value={detail.status}
              onChange={(value) => onStatusChange(detail.id, value)}
              detail={detail}
            />
          </TableCell>
          <TableCell className="py-3 px-3">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem className="gap-2 text-red-600">
                  <Trash2 className="h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>
      )}
    </Draggable>
  );
}

// NewRow component
function NewRow({
  newRowData,
  editingCell,
  setEditingCell,
  onSave,
  onStatusChange,
}: {
  newRowData: {
    id: string;
    title: string;
    actions: string;
    result: string;
    status: BusinessItemDetail["status"];
  };
  editingCell: { id: string; field: string } | null;
  setEditingCell: (cell: { id: string; field: string } | null) => void;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
  onStatusChange: (id: string, status: string) => void;
}) {
  // Create a mock detail object for status determination
  const mockDetail: BusinessItemDetail = {
    id: "new-row",
    title: newRowData.title,
    actions: newRowData.actions,
    result: newRowData.result,
    status: newRowData.status,
  };

  // Check if idea (title) is filled to enable other fields
  const hasIdea = newRowData.title.trim() !== "";

  return (
    <TableRow className="bg-gray-50 dark:bg-gray-900 border-t-2 border-dashed border-b border-gray-200 dark:border-gray-700">
      <TableCell className="py-3 px-3 border-r border-gray-200 dark:border-gray-700">
        <div className="h-4 w-4"></div>
      </TableCell>
      <TableCell className="font-medium py-3 px-3 border-r border-gray-200 dark:border-gray-700 w-1/3">
        <EditableCell
          id="new-row"
          field="title"
          value=""
          multiline={true}
          className="font-semibold"
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={onSave}
          newRowData={newRowData}
        />
      </TableCell>
      <TableCell
        className={`py-3 px-3 border-r border-gray-200 dark:border-gray-700 w-1/3 ${
          !hasIdea ? "relative" : ""
        }`}
      >
        <EditableCell
          id="new-row"
          field="actions"
          value=""
          multiline={true}
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={onSave}
          newRowData={newRowData}
        />
      </TableCell>
      <TableCell
        className={`py-3 px-3 border-r border-gray-200 dark:border-gray-700 w-1/3 ${
          !hasIdea ? "relative" : ""
        }`}
      >
        <EditableCell
          id="new-row"
          field="result"
          value=""
          multiline={true}
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={onSave}
          newRowData={newRowData}
        />
      </TableCell>
      <TableCell className="py-3 px-3 border-r">
        <StatusSelect
          value={newRowData.status}
          onChange={(value) => onStatusChange("new-row", value)}
          placeholder="Status"
          detail={mockDetail}
        />
      </TableCell>
      <TableCell className="py-3 px-3">
        <div className="h-8 w-8"></div>
      </TableCell>
    </TableRow>
  );
}

export function BusinessItemTable({
  itemDetails,
  selectedBusinessItem,
  sectionId,
  topicId,
}: BusinessItemTableProps) {
  const { updateTopicEntry, addTopicEntry } = useBusinessSectionStore();
  const projectId =
    typeof window !== "undefined"
      ? location.pathname.split("/projects/")[1]?.split("/")[0] || ""
      : "";
  const { createEntry, updateEntry } = useTopicEntryMutations(projectId);

  // Local state for reordering
  const [localItemDetails, setLocalItemDetails] =
    useState<BusinessItemDetail[]>(itemDetails);
  const [editingCell, setEditingCell] = useState<{
    id: string;
    field: string;
  } | null>(null);
  const [newRowData, setNewRowData] = useState({
    id: "new-row",
    title: "",
    actions: "",
    result: "",
    status: "idea" as BusinessItemDetail["status"],
  });

  // Update local state when props change
  useEffect(() => {
    console.log("🔥 [BusinessItemTable] Props changed:", {
      itemDetailsCount: itemDetails.length,
      itemDetails: itemDetails.slice(0, 2),
      selectedBusinessItem: selectedBusinessItem?.title,
      sectionId,
      topicId,
    });
    setLocalItemDetails(itemDetails);
  }, [itemDetails, sectionId, topicId]);

  const handleSave = (
    id: string,
    field: keyof BusinessItemDetail,
    value: string
  ) => {
    if (id === "new-row") {
      // Handle new row creation
      const updatedNewRowData = { ...newRowData, [field]: value };

      // Calculate the appropriate status based on filled fields
      const autoStatus = getAutoStatus(
        field === "title" ? value : newRowData.title,
        field === "actions" ? value : newRowData.actions,
        field === "result" ? value : newRowData.result
      );

      // Generate temporary ID for local state, backend will provide real ID
      const tempId = `temp-${Date.now()}`;
      const newItem: BusinessItemDetail = {
        id: tempId,
        title: field === "title" ? value : newRowData.title,
        actions: field === "actions" ? value : newRowData.actions,
        result: field === "result" ? value : newRowData.result,
        status: autoStatus,
        description: "",
        updatedAt: new Date().toISOString(),
      };

      // Update the specific field that was edited
      newItem[field as keyof BusinessItemDetail] = value as any;

      // Only create if there's actual content
      if (value.trim() !== "") {
        const updatedItems = [...localItemDetails, newItem];
        setLocalItemDetails(updatedItems);

        // Update store with new entry
        if (sectionId && topicId) {
          console.log("🔥 [BusinessItemTable] Adding new entry to store:", {
            sectionId,
            topicId,
            newItem,
          });
          addTopicEntry(sectionId, topicId, newItem);
          // Persist to backend
          try {
            const payload = {
              idea: newItem.title,
              action: newItem.actions,
              result: newItem.result,
              status: newItem.status,
              position: 0,
              metadata: {},
            };
            console.log("🔥 [BusinessItemTable] createEntry.mutate →", {
              topicId,
              payload,
            });
            createEntry.mutate(
              {
                topicId: topicId!,
                payload,
              },
              {
                onSuccess: (data: any) => {
                  console.log(
                    "🔥 [BusinessItemTable] createEntry success:",
                    data
                  );
                  // Update local state with real ID from backend
                  if (data?.id) {
                    const updatedItem = { ...newItem, id: String(data.id) };
                    setLocalItemDetails((prev) =>
                      prev.map((item) =>
                        item.id === tempId ? updatedItem : item
                      )
                    );
                    // Update store with real ID
                    updateTopicEntry(sectionId, topicId, tempId, {
                      id: String(data.id),
                    });
                  }
                },
                onError: (error: any) => {
                  console.error(
                    "❌ [BusinessItemTable] createEntry error:",
                    error
                  );
                  // Remove the temporary item on error
                  setLocalItemDetails((prev) =>
                    prev.filter((item) => item.id !== tempId)
                  );
                },
              }
            );
          } catch (e) {
            console.error("❌ [BusinessItemTable] createEntry.mutate error", e);
          }
        } else {
          console.error(
            "❌ [BusinessItemTable] Missing sectionId or topicId for new entry:",
            { sectionId, topicId }
          );
        }

        // Reset new row data
        setNewRowData({
          id: "new-row",
          title: "",
          actions: "",
          result: "",
          status: "idea" as BusinessItemDetail["status"],
        });
      } else {
        // Just update the new row data for display
        setNewRowData(updatedNewRowData);
      }
    } else {
      // Handle existing item update
      const item = localItemDetails.find((item) => item.id === id);
      if (item) {
        const updatedItem = { ...item, [field]: value };

        // Auto-update status based on content
        const autoStatus = getAutoStatus(
          field === "title" ? value : item.title,
          field === "actions" ? value : item.actions,
          field === "result" ? value : item.result
        );

        // Only auto-update status if it's not manually set to confirmed/unproven
        if (
          !isStatusEditable(
            updatedItem.title,
            updatedItem.actions,
            updatedItem.result
          ) ||
          (item.status !== "confirmed" && item.status !== "unproven")
        ) {
          updatedItem.status = autoStatus;
        }

        const updatedItems = localItemDetails.map((existingItem) =>
          existingItem.id === id ? updatedItem : existingItem
        );
        setLocalItemDetails(updatedItems);

        // Update store with changed entry
        if (sectionId && topicId) {
          console.log("🔥 [BusinessItemTable] Updating entry in store:", {
            sectionId,
            topicId,
            id,
            field,
            value,
            status: updatedItem.status,
          });
          updateTopicEntry(sectionId, topicId, id, {
            [field]: value,
            status: updatedItem.status,
          });
          // Only persist to backend if this is a real backend entry (numeric ID)
          if (!id.startsWith("temp-")) {
            try {
              const payload = {
                idea: updatedItem.title,
                action: updatedItem.actions,
                result: updatedItem.result,
                status: updatedItem.status,
                position: 0,
                metadata: {},
              } as const;
              console.log("🔥 [BusinessItemTable] updateEntry.mutate →", {
                topicId,
                entryId: id,
                payload,
              });
              updateEntry.mutate({ topicId: topicId!, entryId: id, payload });
            } catch (e) {
              console.error(
                "❌ [BusinessItemTable] updateEntry.mutate error",
                e
              );
            }
          } else {
            console.log(
              "🔄 [BusinessItemTable] Skipping backend update for temporary entry:",
              id
            );
          }
        } else {
          console.error(
            "❌ [BusinessItemTable] Missing sectionId or topicId for entry update:",
            { sectionId, topicId }
          );
        }
      }
    }
    setEditingCell(null);
  };

  const handleDragEnd = (result: any) => {
    // Check if the drop was outside the droppable area
    if (!result.destination) {
      return;
    }

    const { source, destination } = result;

    // If dropped in the same position, do nothing
    if (source.index === destination.index) {
      return;
    }

    // Reorder items
    const newItems = Array.from(localItemDetails);
    const [reorderedItem] = newItems.splice(source.index, 1);
    newItems.splice(destination.index, 0, reorderedItem);

    setLocalItemDetails(newItems);
  };

  // Handle status change (only for confirmed/unproven when all fields are filled)
  const handleStatusChange = (id: string, newStatus: string) => {
    if (id === "new-row") {
      setNewRowData((prev) => ({
        ...prev,
        status: newStatus as BusinessItemDetail["status"],
      }));
      return;
    }

    const updatedItems = localItemDetails.map((item) =>
      item.id === id
        ? { ...item, status: newStatus as BusinessItemDetail["status"] }
        : item
    );
    setLocalItemDetails(updatedItems);

    // Update store with status change
    if (sectionId && topicId) {
      console.log("🔥 [BusinessItemTable] Updating status in store:", {
        sectionId,
        topicId,
        id,
        newStatus,
      });
      updateTopicEntry(sectionId, topicId, id, {
        status: newStatus as BusinessItemDetail["status"],
      });
    } else {
      console.error(
        "❌ [BusinessItemTable] Missing sectionId or topicId for status change:",
        { sectionId, topicId }
      );
    }
    // updateTopicEntry(selectedBusinessItem?.sectionId || '', selectedBusinessItem?.id || '', id, { status: newStatus as BusinessItemDetail["status"] });
  };

  return (
    <TooltipProvider>
      <div className="w-full max-w-full overflow-hidden">
        <Card className="w-full p-0 overflow-hidden rounded-none border-0 relative z-50">
          <CardContent className="p-0">
            <DragDropContext onDragEnd={handleDragEnd}>
              <div className="w-full overflow-auto max-h-[calc(100vh-200px)] border-b border-gray-200 dark:border-gray-700 relative z-40">
                <Table className="relative z-30">
                  <TableHeader className="sticky top-0 bg-gray-200 dark:bg-gray-800 backdrop-blur-sm z-50">
                    <TableRow className="border-b-2">
                      <TableHead className="bg-gray-200 dark:bg-gray-800 border-r border-gray-300 dark:border-gray-600"></TableHead>
                      <TableHead className="font-semibold border-r border-gray-300 dark:border-gray-600 bg-gray-200 dark:bg-gray-800 w-1/3">
                        Idea
                      </TableHead>
                      <TableHead className="font-semibold border-r border-gray-300 dark:border-gray-600 bg-gray-200 dark:bg-gray-800 w-1/3">
                        Action
                      </TableHead>
                      <TableHead className="font-semibold border-r border-gray-300 dark:border-gray-600 bg-gray-200 dark:bg-gray-800 w-1/3">
                        Result
                      </TableHead>
                      <TableHead className="font-semibold border-r border-gray-300 dark:border-gray-600 bg-gray-200 dark:bg-gray-800 w-32">
                        Status
                      </TableHead>
                      <TableHead className="font-semibold bg-gray-200 dark:bg-gray-800 w-20"></TableHead>
                    </TableRow>
                  </TableHeader>

                  <Droppable droppableId="business-items">
                    {(provided) => (
                      <TableBody
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                      >
                        {localItemDetails.map((detail, index) => (
                          <BusinessItemRow
                            key={detail.id}
                            detail={detail}
                            index={index}
                            editingCell={editingCell}
                            setEditingCell={setEditingCell}
                            onSave={handleSave}
                            onStatusChange={handleStatusChange}
                          />
                        ))}

                        <NewRow
                          newRowData={newRowData}
                          editingCell={editingCell}
                          setEditingCell={setEditingCell}
                          onSave={handleSave}
                          onStatusChange={handleStatusChange}
                        />

                        {provided.placeholder}
                      </TableBody>
                    )}
                  </Droppable>
                </Table>
              </div>
            </DragDropContext>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
